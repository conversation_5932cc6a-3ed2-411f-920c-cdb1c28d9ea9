{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\MemoryReaderGUI.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|solutionrelative:wnbios_poc\\MemoryReaderGUI.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\drv.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|solutionrelative:wnbios_poc\\drv.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\wnbios_poc.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|solutionrelative:wnbios_poc\\wnbios_poc.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\MemoryReaderGUI.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|solutionrelative:wnbios_poc\\MemoryReaderGUI.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\drv.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{43E4E217-67D8-483C-B0F9-12B1B4A35E8D}|wnbios_poc\\wnbios_poc.vcxproj|solutionrelative:wnbios_poc\\drv.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.26100.0\\um\\winnt.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "xutility", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility", "RelativeToolTip": "..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility", "ViewState": "AgIAAHoSAAAAAAAAAAAMwMwSAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-08-02T21:40:16.077Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "winnt.h", "DocumentMoniker": "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.26100.0\\um\\winnt.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\..\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.26100.0\\um\\winnt.h", "ToolTip": "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.26100.0\\um\\winnt.h", "RelativeToolTip": "..\\..\\..\\..\\..\\..\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.26100.0\\um\\winnt.h", "ViewState": "AgIAAOtZAAAAAAAAAAAtwAdaAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-02T17:37:21.037Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "drv.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\drv.cpp", "RelativeDocumentMoniker": "wnbios_poc\\drv.cpp", "ToolTip": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\drv.cpp", "RelativeToolTip": "wnbios_poc\\drv.cpp", "ViewState": "AgIAAB0BAAAAAAAAAAAUwAgCAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-02T14:46:50.041Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "drv.h", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\drv.h", "RelativeDocumentMoniker": "wnbios_poc\\drv.h", "ToolTip": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\drv.h", "RelativeToolTip": "wnbios_poc\\drv.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-03T16:31:08.922Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MemoryReaderGUI.h", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\MemoryReaderGUI.h", "RelativeDocumentMoniker": "wnbios_poc\\MemoryReaderGUI.h", "ToolTip": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\MemoryReaderGUI.h", "RelativeToolTip": "wnbios_poc\\MemoryReaderGUI.h", "ViewState": "AgIAAIQAAAAAAAAAAAAtwIoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-02T21:21:20.075Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MemoryReaderGUI.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\MemoryReaderGUI.cpp", "RelativeDocumentMoniker": "wnbios_poc\\MemoryReaderGUI.cpp", "ToolTip": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\MemoryReaderGUI.cpp", "RelativeToolTip": "wnbios_poc\\MemoryReaderGUI.cpp", "ViewState": "AgIAAMgFAAAAAAAAAAAAwNwFAABiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-02T17:37:23.325Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "wnbios_poc.cpp", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\wnbios_poc.cpp", "RelativeDocumentMoniker": "wnbios_poc\\wnbios_poc.cpp", "ToolTip": "C:\\Users\\<USER>\\Desktop\\驱动漏洞\\vstest\\wnbios_poc-main\\wnbios_poc\\wnbios_poc.cpp", "RelativeToolTip": "wnbios_poc\\wnbios_poc.cpp", "ViewState": "AgIAACUAAAAAAAAAAAAIwB4AAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-02T13:59:41.66Z"}]}]}]}