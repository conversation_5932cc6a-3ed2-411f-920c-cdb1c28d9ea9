#pragma once
#include <vector>
#include <string>

// 三角洲游戏数据结构定义
struct Vector3 {
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    float distance(const Vector3& other) const;
    bool is_zero() const { return x == 0 && y == 0 && z == 0; }
};

struct DeltaEntity {
    uintptr_t entity_ptr;
    Vector3 position;
    Vector3 head_position;
    Vector3 velocity;
    float health;
    float max_health;
    float armor;
    int team_id;
    int player_state;
    int weapon_id;
    bool is_valid;
    bool is_player;
    bool is_alive;
    bool is_visible;
    std::string name;
    
    DeltaEntity();
    bool is_enemy(int local_team) const { return team_id != local_team && team_id > 0; }
    float health_percentage() const { return max_health > 0 ? (health / max_health) * 100.0f : 0; }
};

// 游戏状态信息
struct GameState {
    Vector3 local_position;
    int local_team;
    bool in_game;
    int round_time;
    int score_team1;
    int score_team2;
    
    GameState() : local_team(0), in_game(false), round_time(0), score_team1(0), score_team2(0) {}
};

// 三角洲游戏偏移量配置
namespace DeltaOffsets {
    // 基础偏移量
    extern const uintptr_t ENTITY_LIST;
    extern const uintptr_t LOCAL_PLAYER;
    extern const uintptr_t GAME_STATE;
    extern const uintptr_t VIEW_MATRIX;
    extern const uintptr_t ENTITY_SIZE;
    extern const int MAX_ENTITIES;
    
    // 实体偏移量
    extern const uintptr_t POSITION_X;
    extern const uintptr_t POSITION_Y;
    extern const uintptr_t POSITION_Z;
    extern const uintptr_t HEAD_X;
    extern const uintptr_t HEAD_Y;
    extern const uintptr_t HEAD_Z;
    extern const uintptr_t VELOCITY_X;
    extern const uintptr_t VELOCITY_Y;
    extern const uintptr_t VELOCITY_Z;
    extern const uintptr_t HEALTH;
    extern const uintptr_t MAX_HEALTH;
    extern const uintptr_t ARMOR;
    extern const uintptr_t TEAM_ID;
    extern const uintptr_t PLAYER_STATE;
    extern const uintptr_t WEAPON_ID;
    extern const uintptr_t NAME_PTR;
    extern const uintptr_t IS_VISIBLE;
}

// 实体管理器类
class DeltaEntityManager {
private:
    class eneio_lib* driver;
    uintptr_t game_base;
    GameState game_state;
    
    bool is_valid_entity(uintptr_t entity_ptr);
    bool is_player_entity(uintptr_t entity_ptr);
    std::string read_entity_name(uintptr_t entity_ptr);
    void update_game_state();
    
public:
    DeltaEntityManager(class eneio_lib* drv);
    
    // 核心功能
    std::vector<DeltaEntity> get_all_entities();
    DeltaEntity read_entity(uintptr_t entity_ptr);
    GameState get_game_state() { return game_state; }
    
    // 过滤功能
    std::vector<DeltaEntity> filter_by_distance(const std::vector<DeltaEntity>& entities, float max_distance);
    std::vector<DeltaEntity> filter_by_team(const std::vector<DeltaEntity>& entities, int team_id);
    std::vector<DeltaEntity> filter_players_only(const std::vector<DeltaEntity>& entities);
    std::vector<DeltaEntity> filter_enemies_only(const std::vector<DeltaEntity>& entities);
    std::vector<DeltaEntity> filter_alive_only(const std::vector<DeltaEntity>& entities);
    
    // 显示功能
    void print_entity_info(const DeltaEntity& entity);
    void print_entity_summary(const DeltaEntity& entity);
    void print_game_state();
    
    // 实用功能
    bool is_game_running() { return game_base != 0; }
    uintptr_t get_game_base() { return game_base; }
};

// 扫描器配置
struct ScannerConfig {
    bool show_players;
    bool show_ai;
    bool show_enemies_only;
    bool show_alive_only;
    float max_distance;
    int target_team;
    int update_interval_ms;
    bool export_enabled;
    std::string export_filename;
    
    ScannerConfig() : show_players(true), show_ai(true), show_enemies_only(false),
                     show_alive_only(true), max_distance(1000.0f), target_team(-1),
                     update_interval_ms(1000), export_enabled(false), export_filename("entities.txt") {}
};

// 主要功能函数
void print_menu();
void single_scan(DeltaEntityManager& manager, const ScannerConfig& config);
void continuous_monitoring(DeltaEntityManager& manager, const ScannerConfig& config);
void interactive_filter(DeltaEntityManager& manager);
void export_entities(const std::vector<DeltaEntity>& entities, const std::string& filename);
void print_statistics(const std::vector<DeltaEntity>& entities);

// 实用函数
std::vector<DeltaEntity> apply_filters(const std::vector<DeltaEntity>& entities, 
                                      const ScannerConfig& config, 
                                      const GameState& game_state);
void clear_screen();
std::string get_timestamp();
