# Delta Force Entity Scanner

基于wnBios64驱动的三角洲游戏实体扫描工具，能够实时获取游戏中的所有实体信息。

## 功能特性

### 🎯 核心功能
- **实体检测**: 自动识别游戏中的所有实体（玩家、AI、NPC）
- **实时监控**: 持续监控实体状态变化
- **智能分类**: 自动区分真实玩家和AI机器人
- **位置追踪**: 获取实体的精确3D坐标和头部位置
- **状态监控**: 监控血量、护甲、队伍、武器等信息

### 🔍 高级功能
- **多重过滤**: 按距离、队伍、类型、状态等条件过滤
- **搜索功能**: 按名称、血量、武器等条件搜索实体
- **数据导出**: 将扫描结果导出到文件
- **统计分析**: 提供详细的实体统计信息
- **游戏状态**: 显示当前游戏状态和本地玩家信息

## 文件说明

### 主要程序
- `delta_entity_scanner.cpp` - 简单版本扫描器
- `delta_advanced_scanner.cpp` - 高级版本扫描器（推荐）
- `delta_scanner.h` - 头文件定义
- `drv.h` / `drv.cpp` - wnBios64驱动接口

### 编译文件
- `build_scanner.bat` - 自动编译脚本
- `wnbios_poc.sln` - Visual Studio解决方案

## 使用方法

### 1. 编译程序

#### 方法一：使用批处理脚本（推荐）
```batch
# 在Visual Studio Developer Command Prompt中运行
build_scanner.bat
```

#### 方法二：手动编译
```batch
# 编译高级版本
cl /EHsc /std:c++17 /I. delta_advanced_scanner.cpp drv.cpp /Fe:delta_scanner.exe /link kernel32.lib user32.lib
```

### 2. 运行程序

1. **以管理员身份运行** - 驱动程序需要管理员权限
2. **启动Delta Force游戏** - 确保游戏正在运行
3. **运行扫描器** - 执行编译好的exe文件

```batch
# 运行高级扫描器
delta_advanced_scanner.exe
```

## 功能菜单

### 主菜单选项
```
1. Single scan           - 执行单次扫描
2. Continuous monitoring - 持续监控模式
3. Configure filters     - 配置过滤器
4. Show game state      - 显示游戏状态
5. Export entities      - 导出实体数据
6. Show statistics      - 显示统计信息
7. Advanced search      - 高级搜索功能
0. Exit                 - 退出程序
```

### 过滤器配置
- **Show Players**: 显示真实玩家
- **Show AI**: 显示AI机器人
- **Enemies Only**: 仅显示敌方实体
- **Alive Only**: 仅显示存活实体
- **Max Distance**: 最大距离过滤
- **Target Team**: 指定队伍过滤
- **Update Interval**: 更新间隔设置

### 高级搜索功能
- **按名称搜索**: 根据实体名称查找
- **查找最近实体**: 找到距离最近的实体
- **血量范围搜索**: 按血量范围筛选
- **武器类型搜索**: 按武器ID筛选
- **移动实体搜索**: 查找正在移动的实体

## 数据结构

### 实体信息
```cpp
struct DeltaEntity {
    uintptr_t entity_ptr;      // 实体内存地址
    Vector3 position;          // 3D位置坐标
    Vector3 head_position;     // 头部位置坐标
    Vector3 velocity;          // 移动速度向量
    float health;              // 当前血量
    float max_health;          // 最大血量
    float armor;               // 护甲值
    int team_id;               // 队伍ID
    int player_state;          // 玩家状态值
    int weapon_id;             // 武器ID
    bool is_player;            // 是否为真实玩家
    bool is_alive;             // 是否存活
    bool is_visible;           // 是否可见
    string name;               // 实体名称
};
```

### 游戏状态
```cpp
struct GameState {
    Vector3 local_position;    // 本地玩家位置
    int local_team;            // 本地玩家队伍
    bool in_game;              // 是否在游戏中
    int round_time;            // 回合时间
    int score_team1;           // 队伍1分数
    int score_team2;           // 队伍2分数
};
```

## 技术原理

### 内存偏移量（基于技术文档v2.0）
```cpp
// 基础偏移量
ENTITY_LIST = 0x4D28D58;     // 实体列表基址
LOCAL_PLAYER = 0x4D28C40;    // 本地玩家基址
GAME_STATE = 0x4D28E00;      // 游戏状态基址

// 实体偏移量
POSITION_X = 0x1A0;          // 位置X坐标
HEALTH = 0x140;              // 血量
TEAM_ID = 0x3C8;             // 队伍ID
PLAYER_STATE = 0x3D8;        // 玩家状态
```

### 玩家识别算法
- **PlayerState值分析**:
  - 1-50: 真实玩家
  - 51-100: AI机器人
  - 0或其他: 无效实体

### 驱动程序功能
- 利用wnBios64.sys漏洞进行内核级内存访问
- 支持任意虚拟内存读写
- 自动处理服务创建、启动、停止、清理
- 兼容多个Windows版本

## 安全注意事项

### ⚠️ 重要警告
1. **仅用于学习研究** - 请勿用于实际游戏作弊
2. **管理员权限** - 程序需要管理员权限运行
3. **反作弊检测** - 可能被游戏反作弊系统检测
4. **系统稳定性** - 内核级操作可能影响系统稳定性

### 🛡️ 使用建议
- 在虚拟机或测试环境中使用
- 定期备份重要数据
- 了解相关法律法规
- 仅用于授权的安全研究

## 故障排除

### 常见问题

**Q: 程序提示"Driver failed to load"**
A: 确保以管理员身份运行，检查Windows版本兼容性

**Q: 找不到Delta Force进程**
A: 确保游戏正在运行，检查进程名称是否正确

**Q: 扫描不到实体**
A: 检查游戏版本是否匹配，偏移量可能需要更新

**Q: 程序崩溃或蓝屏**
A: 检查内存地址有效性，可能需要更新偏移量

### 调试模式
程序包含详细的调试输出，可以帮助诊断问题：
- 驱动加载状态
- 内存读取结果
- 实体验证过程
- 错误信息详情

## 开发信息

### 版本历史
- v1.0: 基础实体扫描功能
- v2.0: 添加高级过滤和搜索功能
- v2.1: 优化用户界面和错误处理

### 技术栈
- **语言**: C++17
- **编译器**: MSVC (Visual Studio)
- **平台**: Windows 10/11 x64
- **驱动**: wnBios64.sys

### 贡献指南
欢迎提交问题报告和功能建议，但请注意：
- 仅接受安全研究相关的改进
- 不接受用于作弊的功能请求
- 遵循负责任的披露原则

## 免责声明

本工具仅用于安全研究和教育目的。使用者需要：
- 遵守当地法律法规
- 获得适当的授权
- 承担使用风险
- 不用于恶意目的

开发者不对任何滥用行为承担责任。
