#define _CRT_SECURE_NO_WARNINGS
#include <Windows.h>
#include <map>
#include <signal.h>
#include <time.h>

#include "drv.h"
#include "MemoryReaderGUI.h"

using namespace System;
using namespace System::Windows::Forms;

// 全局驱动指针用于信号处理
eneio_lib* g_driver = nullptr;

// 信号处理函数
void signal_handler(int signal) {
	printf("\n[*] Received interrupt signal (%d). Cleaning up...\n", signal);
	if (g_driver) {
		delete g_driver;
		g_driver = nullptr;
	}
	exit(0);
}

// 安全的字符串输入函数
void safe_input(char* buffer, size_t buffer_size, const char* prompt) {
	printf("%s", prompt);
	if (fgets(buffer, (int)buffer_size, stdin)) {
		// 移除换行符
		size_t len = strlen(buffer);
		if (len > 0 && buffer[len - 1] == '\n') {
			buffer[len - 1] = '\0';
		}
	}
}

// 获取当前时间字符串
void get_timestamp(char* buffer, size_t buffer_size) {
	time_t rawtime;
	struct tm* timeinfo;
	time(&rawtime);
	timeinfo = localtime(&rawtime);
	strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", timeinfo);
}

int main(int argc, char* argv[])
{
	// Check for console mode flag
	bool consoleMode = false;
	for (int i = 1; i < argc; i++)
	{
		if (strcmp(argv[i], "--console") == 0)
		{
			consoleMode = true;
			break;
		}
	}

	if (!consoleMode)
	{
		// Launch GUI mode
		Application::EnableVisualStyles();
		Application::SetCompatibleTextRenderingDefault(false);

		MemoryReaderForm^ form = gcnew MemoryReaderForm();
		Application::Run(form);

		return 0;
	}

	// Console mode
	printf("[*] Starting eneio64 driver test (Console Mode)...\n");
	
	// 设置信号处理器
	signal(SIGINT, signal_handler);
	signal(SIGTERM, signal_handler);
	
	try {
		eneio_lib driver;
		g_driver = &driver;
		
		// 首先列出所有运行的进程
		printf("[*] Listing all running processes...\n");
		driver.list_running_processes();
		
		// 设置扫描次数
		char scan_count_input[32] = { 0 };
		int scan_count = 1;
		printf("\n==================================================\n");
		printf("[*] SCAN CONFIGURATION\n");
		printf("[*] Enter scan count (0 = infinite loop, 1+ = specific count): ");
		safe_input(scan_count_input, sizeof(scan_count_input), "");
		scan_count = atoi(scan_count_input);
		
		if (scan_count == 0) {
			printf("[+] Infinite scan mode enabled (Ctrl+C to stop)\n");
		} else {
			printf("[+] Will perform %d scan(s)\n", scan_count);
		}
		
		// 交互式选择进程
		char process_name[256] = { 0 };
		printf("\n==================================================\n");
		printf("[*] PROCESS ANALYZER - Interactive Mode\n");
		printf("[*] Common processes you can analyze:\n");
		printf("    - notepad.exe (Notepad)\n");
		printf("    - calc.exe (Calculator)\n");
		printf("    - explorer.exe (Windows Explorer)\n");
		printf("    - chrome.exe (Google Chrome)\n");
		printf("    - firefox.exe (Mozilla Firefox)\n");
		printf("    - cmd.exe (Command Prompt)\n");
		printf("\n[*] CONFIRMED AVAILABLE PROCESSES:\n");
		printf("    - notepad.exe (PID 16168) ?\n");
		printf("    - Taskmgr.exe (Task Manager) ?\n");
		printf("    - browser.exe ?\n");
		printf("    - wps.exe ?\n");
		printf("\n[*] NOTE: DeltaForceClient-Win64-Shipping.exe was NOT found in kernel process list\n");
		printf("    This suggests the process may have anti-debug protection\n");
		printf("\n");
		safe_input(process_name, sizeof(process_name), "[*] Enter the process name to analyze: ");
		
		// 开始扫描循环
		int current_scan = 0;
		int consecutive_failures = 0;
		bool continue_scanning = true;
		
		while (continue_scanning) {
			current_scan++;
			
			// 获取时间戳
			char timestamp[64] = { 0 };
			get_timestamp(timestamp, sizeof(timestamp));
			
			if (scan_count > 0) {
				printf("\n[*] === SCAN %d/%d [%s] ===\n", current_scan, scan_count, timestamp);
			} else {
				printf("\n[*] === SCAN %d (INFINITE MODE) [%s] ===\n", current_scan, timestamp);
			}
			
			printf("[*] Analyzing process: %s\n", process_name);
			
			printf("[*] Searching for process: %s\n", process_name);
			
			// 尝试不同的进程名变体
			uintptr_t base = driver.get_process_base(process_name);
			
			// 如果没找到，尝试截断版本（前15个字符）
			if (!base && strlen(process_name) > 15) {
				char truncated_name[16] = { 0 };
				strncpy(truncated_name, process_name, 15);
				printf("[*] Trying truncated name: %s\n", truncated_name);
				base = driver.get_process_base(truncated_name);
			}
			
			// 如果还没找到，尝试不带.exe的版本
			if (!base) {
				char* dot_pos = strrchr(process_name, '.');
				if (dot_pos) {
					char name_without_ext[256] = { 0 };
					size_t len = dot_pos - process_name;
					strncpy(name_without_ext, process_name, len);
					printf("[*] Trying without extension: %s\n", name_without_ext);
					base = driver.get_process_base(name_without_ext);
				}
			}
			
			if (!base)
			{
				printf("[-] Process '%s' is not running or not found\n", process_name);
				printf("[*] This could be due to:\n");
				printf("    1. Process name mismatch (check exact name)\n");
				printf("    2. Process protection/anti-debug\n");
				printf("    3. Insufficient privileges\n");
				
				// 尝试搜索相关进程
				printf("[*] Searching for processes containing 'delta':\n");
				driver.search_processes_by_keyword("delta");
				
				printf("\n[*] Searching for processes containing 'force':\n");
				driver.search_processes_by_keyword("force");
				
				printf("\n[*] Showing all processes for reference:\n");
				driver.list_running_processes();
				
				if (scan_count == 0) {
					printf("[*] Waiting 2 seconds before next scan...\n");
					Sleep(2000);
					continue;
				} else {
					printf("[-] Please check the process name and try again\n");
					system("pause");
					return false;
				}
			}

			printf("[+] %s base address: 0x%llx\n", process_name, base);

			// 读取PE头的前16个字节
			UINT8 buf[16] = { 0 };
			bool success = driver.read_virtual_memory(base, buf, 16);
			
			if (success) {
				printf("[+] PE Header (first 16 bytes): ");
				for (int i = 0; i < 16; i++) {
					printf("%02X ", buf[i]);
				}
				printf("\n");
				
				// 检查是否是有效的PE文件
				if (buf[0] == 0x4D && buf[1] == 0x5A) {
					printf("[+] Valid PE file detected (MZ signature found)\n");
				} else {
					printf("[-] Invalid PE file or read failed\n");
					printf("[-] Expected MZ signature (4D 5A), got: %02X %02X\n", buf[0], buf[1]);
				}
			} else {
				printf("[-] Failed to read memory from address 0x%llx\n", base);
			}
			
			// 枚举进程模块
			printf("\n[*] Enumerating process modules (showing first 10)...\n");
			driver.enumerate_process_modules(process_name);
			
			// 检查是否继续扫描
			if (scan_count > 0 && current_scan >= scan_count) {
				continue_scanning = false;
			} else if (scan_count == 0) {
				printf("\n[*] Scan %d completed. Waiting 3 seconds before next scan...\n", current_scan);
				printf("[*] Press Ctrl+C to stop infinite scanning\n");
				Sleep(3000);
			}
		}
		
		printf("\n[*] All scans completed (%d total scans)\n", current_scan);
		
		// 询问是否要分析其他进程
		printf("\n==================================================\n");
		printf("[*] Analysis completed for '%s'\n", process_name);
		
		char choice_input[10] = { 0 };
		safe_input(choice_input, sizeof(choice_input), "[*] Do you want to analyze another process? (y/n): ");
		char choice = choice_input[0];
		
		if (choice == 'y' || choice == 'Y') {
			printf("\n============================================================\n");
			printf("[*] Starting new analysis session...\n");
			
			// 重新列出进程
			driver.list_running_processes();
			
			// 重新设置扫描次数
			safe_input(scan_count_input, sizeof(scan_count_input), "\n[*] Enter scan count (0 = infinite, 1+ = specific): ");
			scan_count = atoi(scan_count_input);
			
			// 再次输入进程名
			safe_input(process_name, sizeof(process_name), "[*] Enter the process name to analyze: ");
			
			// 重新开始扫描循环
			current_scan = 0;
			continue_scanning = true;
			
			while (continue_scanning) {
				current_scan++;
				
				if (scan_count > 0) {
					printf("\n[*] === SCAN %d/%d ===\n", current_scan, scan_count);
				} else {
					printf("\n[*] === SCAN %d (INFINITE MODE) ===\n", current_scan);
				}
				
				uintptr_t base2 = driver.get_process_base(process_name);
				if (base2) {
					printf("[+] %s base address: 0x%llx\n", process_name, base2);
					driver.enumerate_process_modules(process_name);
				} else {
					printf("[-] Process '%s' not found\n", process_name);
					if (scan_count == 0) {
						printf("[*] Waiting 2 seconds before next scan...\n");
						Sleep(2000);
						continue;
					} else {
						break;
					}
				}
				
				// 检查是否继续扫描
				if (scan_count > 0 && current_scan >= scan_count) {
					continue_scanning = false;
				} else if (scan_count == 0) {
					printf("\n[*] Scan %d completed. Waiting 3 seconds before next scan...\n", current_scan);
					Sleep(3000);
				}
			}
		}
		
		printf("\n==================================================\n");
		printf("[*] All analysis completed. Press any key to exit and cleanup...\n");
		system("pause");
		
		printf("\n[*] Exiting and cleaning up resources...\n");
		g_driver = nullptr;  // 清理全局指针
		// 析构函数会自动调用清理代码
		return true;
	}
	catch (const std::exception& e) {
		printf("[-] Exception occurred: %s\n", e.what());
		printf("[*] Press any key to exit...\n");
		system("pause");
		return false;
	}
	catch (...) {
		printf("[-] Unknown exception occurred during driver initialization\n");
		printf("[*] Press any key to exit...\n");
		system("pause");
		return false;
	}
}


bool eneio_lib::leak_kpointers(std::vector<uintptr_t>& pointers)
{
	const unsigned long SystemExtendedHandleInformation = 0x40;

	unsigned long buffer_length = 0;
	unsigned char tempbuffer[1024] = { 0 };
	NTSTATUS status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), &tempbuffer, sizeof(tempbuffer), &buffer_length);

	buffer_length += 50 * (sizeof(SYSTEM_HANDLE_INFORMATION_EX) + sizeof(SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX));

	PVOID buffer = VirtualAlloc(nullptr, buffer_length, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);

	memset(buffer, 0, buffer_length);

	unsigned long buffer_length_correct = 0;
	status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), buffer, buffer_length, &buffer_length_correct);

	SYSTEM_HANDLE_INFORMATION_EX* handle_information = reinterpret_cast<SYSTEM_HANDLE_INFORMATION_EX*>(buffer);

	for (unsigned int i = 0; i < handle_information->NumberOfHandles; i++)
	{
		const unsigned int SystemUniqueReserved = 4;
		const unsigned int SystemKProcessHandleAttributes = 0x102A;

		if (handle_information->Handles[i].UniqueProcessId == SystemUniqueReserved &&
			handle_information->Handles[i].HandleAttributes == SystemKProcessHandleAttributes)
		{
			pointers.push_back(reinterpret_cast<uintptr_t>(handle_information->Handles[i].Object));
		}
	}

	VirtualFree(buffer, 0, MEM_RELEASE);
	return true;
}


uintptr_t eneio_lib::map_physical(uint64_t address, size_t size, eneio_mem& mem)
{
	memset(&mem, 0, sizeof(eneio_mem));
	mem.addr = address;
	mem.size = size;
	DWORD retSize;
	auto status = DeviceIoControl(hHandle, 0x80102040, &mem, sizeof(eneio_mem), &mem, sizeof(eneio_mem), &retSize, 0);
	if (!status)
		return 0;
	
	return mem.outPtr;
}

uintptr_t eneio_lib::unmap_physical(eneio_mem& mem)
{
	DWORD bytes_returned;
	auto status = DeviceIoControl(hHandle, 0x80102044, &mem, sizeof(eneio_mem), 0, 0, &bytes_returned, 0);
	if (!status)
		return 0;

	return 1;
}

uintptr_t eneio_lib::get_system_dirbase()
{
	for (int i = 0; i < 10; i++)
	{
		eneio_mem mem;
		uintptr_t lpBuffer = map_physical(i * 0x10000, 0x10000, mem);

		for (int uOffset = 0; uOffset < 0x10000; uOffset += 0x1000)
		{
			if (0x00000001000600E9 ^ (0xffffffffffff00ff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset)))
				continue;
			if (0xfffff80000000000 ^ (0xfffff80000000000 & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0x70)))
				continue;
			if (0xffffff0000000fff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0))
				continue;

			return *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0);
		}

		unmap_physical(mem);
	}

	return NULL;
}

uintptr_t eneio_lib::get_process_id(const char* image_name)
{
	HANDLE hsnap;
	PROCESSENTRY32 pt;
	DWORD PiD;
	hsnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	pt.dwSize = sizeof(PROCESSENTRY32);
	do {
		if (!strcmp(pt.szExeFile, image_name)) {
			CloseHandle(hsnap);
			PiD = pt.th32ProcessID;
			return PiD;
			if (PiD != NULL) {
				return 0;
			}
		}
	} while (Process32Next(hsnap, &pt));
	return 1;
}

uintptr_t eneio_lib::get_process_base(const char* image_name)
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();

	if (!kprocess_initial)
		return NULL;

	printf("system_kprocess: %llx\n", kprocess_initial);
	printf("system_cr3: %llx\n", cr3);

	const unsigned long limit = 800; // 增加遍历限制以找到更多进程

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	uintptr_t image_base_out = 0;


	// 使用更安全的遍历方法
	std::vector<uintptr_t> seen_kprocess;
	std::vector<int> seen_pids;
	
	uintptr_t current_kprocess = kprocess_initial;
	const int max_processes = 600;
	
	for (int i = 0; i < max_processes; i++)
	{
		// 检查是否已访问过
		bool already_visited = false;
		for (uintptr_t addr : seen_kprocess) {
			if (addr == current_kprocess) {
				already_visited = true;
				break;
			}
		}
		
		if (already_visited) {
			printf("[DEBUG] Loop detected, stopping\n");
			break;
		}
		
		seen_kprocess.push_back(current_kprocess);

		uintptr_t virtual_size = read_virtual_memory<uintptr_t>(current_kprocess + EP_VIRTUALSIZE);
		if (virtual_size == 0) {
			// 获取下一个进程
			uintptr_t flink = 0;
			read_virtual_memory(current_kprocess + EP_ACTIVEPROCESSLINK, &flink, sizeof(flink));
			if (flink == 0) break;
			current_kprocess = flink - EP_ACTIVEPROCESSLINK;
			continue;
		}

		uintptr_t directory_table = read_virtual_memory<uintptr_t>(current_kprocess + EP_DIRECTORYTABLE);
		uintptr_t base_address = read_virtual_memory<uintptr_t>(current_kprocess + EP_SECTIONBASE);

		char name[16] = { };
		read_virtual_memory(current_kprocess + EP_IMAGEFILENAME, &name, sizeof(name));

		int process_id = 0;
		read_virtual_memory(current_kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		// 验证进程ID有效性
		if (process_id <= 0 || process_id >= 100000) {
			// 获取下一个进程
			uintptr_t flink = 0;
			read_virtual_memory(current_kprocess + EP_ACTIVEPROCESSLINK, &flink, sizeof(flink));
			if (flink == 0) break;
			current_kprocess = flink - EP_ACTIVEPROCESSLINK;
			continue;
		}

		// 检查重复PID
		bool already_seen = false;
		for (int pid : seen_pids) {
			if (pid == process_id) {
				already_seen = true;
				break;
			}
		}
		
		if (already_seen) {
			printf("[DEBUG] Duplicate PID %d, stopping\n", process_id);
			break;
		}
		
		seen_pids.push_back(process_id);

		// 改进的进程名称匹配逻辑
		bool name_match = false;
		
		if (strlen(name) > 0 && strncmp(name, image_name, strlen(name)) == 0) {
			name_match = true;
		}
		else if (strlen(name) > 0 && strstr(image_name, name) != NULL) {
			name_match = true;
		}
		else if (strncmp(name, "DeltaForceClie", 14) == 0 && strstr(image_name, "DeltaForce") != NULL) {
			name_match = true;
		}
		
		// 只显示相关进程
		if (strstr(image_name, "Delta") != NULL || strstr(name, "Delta") != NULL || name_match) {
			printf("[DEBUG] Checking process: kernel='%s', input='%s', PID=%d\n", name, image_name, process_id);
		}
		
		if (name_match && process_id == get_process_id(image_name))
		{
			printf("[FOUND] Matching process:\n");
			printf("  Kernel name: %s\n", name);
			printf("  Full name: %s\n", image_name);
			printf("  PID: %d\n", process_id);
			printf("  Base: 0x%llx\n", base_address);
			printf("  CR3: 0x%llx\n", directory_table);

			image_base_out = base_address;
			cr3 = directory_table;
			attached_proc = process_id;

			break;
		}
		
		// 获取下一个进程
		uintptr_t flink = 0;
		read_virtual_memory(current_kprocess + EP_ACTIVEPROCESSLINK, &flink, sizeof(flink));
		if (flink == 0) break;
		current_kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		// 检查是否回到起始点
		if (current_kprocess == kprocess_initial && i > 0) {
			printf("[DEBUG] Completed full circle\n");
			break;
		}
	}
	
	return image_base_out;
}

bool eneio_lib::read_physical_memory(uintptr_t physical_address, void* output, unsigned long size)
{
	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(output, reinterpret_cast<void*>(virtual_address), size);
	unmap_physical(mem);
	return true;
}

bool eneio_lib::write_physical_memory(uintptr_t physical_address, void* data, unsigned long size)
{
	if (!data)
		return false;

	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(reinterpret_cast<void*>(virtual_address), reinterpret_cast<void*>(data), size);
	unmap_physical(mem);
	return true;
}

uintptr_t eneio_lib::convert_virtual_to_physical(uintptr_t virtual_address)
{
	uintptr_t va = virtual_address;

	unsigned short PML4 = (unsigned short)((va >> 39) & 0x1FF);
	uintptr_t PML4E = 0;
	read_physical_memory((cr3 + PML4 * sizeof(uintptr_t)), &PML4E, sizeof(PML4E));

	if (PML4E == 0)
		return 0;

	unsigned short DirectoryPtr = (unsigned short)((va >> 30) & 0x1FF);
	uintptr_t PDPTE = 0;
	read_physical_memory(((PML4E & 0xFFFFFFFFFF000) + DirectoryPtr * sizeof(uintptr_t)), &PDPTE, sizeof(PDPTE));

	if (PDPTE == 0)
		return 0;

	if ((PDPTE & (1 << 7)) != 0)
		return (PDPTE & 0xFFFFFC0000000) + (va & 0x3FFFFFFF);

	unsigned short Directory = (unsigned short)((va >> 21) & 0x1FF);

	uintptr_t PDE = 0;
	read_physical_memory(((PDPTE & 0xFFFFFFFFFF000) + Directory * sizeof(uintptr_t)), &PDE, sizeof(PDE));

	if (PDE == 0)
		return 0;

	if ((PDE & (1 << 7)) != 0)
	{
		return (PDE & 0xFFFFFFFE00000) + (va & 0x1FFFFF);
	}

	unsigned short Table = (unsigned short)((va >> 12) & 0x1FF);
	uintptr_t PTE = 0;

	read_physical_memory(((PDE & 0xFFFFFFFFFF000) + Table * sizeof(uintptr_t)), &PTE, sizeof(PTE));

	if (PTE == 0)
		return 0;

	return (PTE & 0xFFFFFFFFFF000) + (va & 0xFFF);
}

bool eneio_lib::read_virtual_memory(uintptr_t address, LPVOID output, unsigned long size)
{
	if (!address)
		return false;

	if (!size)
		return false;

	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	bool result = read_physical_memory(physical_address, output, size);
	
	return result;
}

bool eneio_lib::write_virtual_memory(uintptr_t address, LPVOID data, unsigned long size)
{
	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	write_physical_memory(physical_address, data, size);
	return true;
}

uintptr_t eneio_lib::get_process_peb(const char* process_name)
{
	printf("[DEBUG] Getting PEB for process: %s\n", process_name);

	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[ERROR] Failed to get system CR3\n");
		return NULL;
	}
	printf("[DEBUG] System CR3: 0x%llx\n", cr3);

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[ERROR] Failed to leak initial KPROCESS\n");
		return NULL;
	}
	printf("[DEBUG] Initial KPROCESS: 0x%llx\n", kprocess_initial);

	// First, let's check what PID we're looking for
	uintptr_t target_pid = get_process_id(process_name);
	printf("[DEBUG] Target PID from get_process_id: %llu\n", target_pid);

	if (target_pid == 0) {
		printf("[ERROR] Could not find process ID for %s\n", process_name);
		return NULL;
	}

	const unsigned long limit = 800; // 增加限制以匹配进程枚举的限制
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;

	printf("[DEBUG] Starting EPROCESS traversal, limit: %lu\n", limit);

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;

		char name[16] = { };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		name[15] = '\0'; // Ensure null termination

		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		// Only show debug info for processes that might match
		if (strstr(process_name, "Delta") || strstr(name, "Delta") || strncmp(name, "DeltaForce", 10) == 0) {
			printf("[DEBUG] Found potential match: kernel='%s', target='%s', PID=%d, target_PID=%llu\n",
				   name, process_name, process_id, target_pid);
		}

		// For long process names, check if the truncated name matches the beginning
		// or if the full process name contains the truncated name
		bool name_matches = false;
		if (strlen(name) >= 15) {
			// Name is truncated, check if process_name starts with this truncated name
			name_matches = (strncmp(process_name, name, 15) == 0);
			if (strstr(process_name, "Delta")) {
				printf("[DEBUG] Truncated name check: '%s' vs '%s' (first 15 chars) = %s\n",
					   process_name, name, name_matches ? "MATCH" : "NO MATCH");
			}
		} else {
			// Name is not truncated, use normal matching
			name_matches = (strstr(process_name, name) != nullptr);
		}

		if (name_matches && process_id == target_pid)
		{
			printf("[SUCCESS] Found matching process!\n");
			printf("  Kernel name: %s\n", name);
			printf("  Target name: %s\n", process_name);
			printf("  PID: %d\n", process_id);
			printf("  KPROCESS: 0x%llx\n", kprocess);

			// 尝试不同的PEB偏移 (不同Windows版本可能不同)
			uintptr_t peb_address = 0;
			bool peb_read_success = false;

			// 常见的PEB偏移
			uintptr_t peb_offsets[] = {0x550, 0x520, 0x4f8, 0x3f8};
			int num_offsets = sizeof(peb_offsets) / sizeof(peb_offsets[0]);

			for (int i = 0; i < num_offsets; i++) {
				peb_read_success = read_virtual_memory(kprocess + peb_offsets[i], &peb_address, sizeof(peb_address));
				printf("  Trying PEB offset 0x%llx: %s, address: 0x%llx\n",
					   peb_offsets[i], peb_read_success ? "SUCCESS" : "FAILED", peb_address);

				if (peb_read_success && peb_address != 0 && peb_address > 0x1000) {
					printf("  Found valid PEB at offset 0x%llx: 0x%llx\n", peb_offsets[i], peb_address);
					break;
				}
				peb_address = 0;
			}

			if (!peb_read_success || peb_address == 0) {
				printf("[ERROR] Failed to read PEB address with any known offset\n");
				return NULL;
			}

			uintptr_t directory_table = read_virtual_memory<uintptr_t>(kprocess + EP_DIRECTORYTABLE);
			printf("  Directory table (CR3): 0x%llx\n", directory_table);

			cr3 = directory_table;
			attached_proc = process_id;

			return peb_address;
		}
	}

	printf("[ERROR] Process not found in EPROCESS list after checking %lu entries\n", limit);
	return NULL;
}

void eneio_lib::enumerate_process_modules(const char* process_name)
{
    printf("[*] Enumerating modules for process: %s\n", process_name);
    
    uintptr_t peb = get_process_peb(process_name);
    if (!peb) {
        printf("[-] Failed to get PEB for process %s\n", process_name);
        return;
    }
    
    printf("[+] PEB address: 0x%llx\n", peb);
    
    // PEB + 0x18 = PEB_LDR_DATA
    uintptr_t ldr_data_ptr = 0;
    if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
        printf("[-] Failed to read LDR data pointer\n");
        return;
    }
    
    printf("[+] LDR data address: 0x%llx\n", ldr_data_ptr);
    
    // PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
    uintptr_t module_list = 0;
    if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
        printf("[-] Failed to read module list\n");
        return;
    }
    
    printf("[+] Module list head: 0x%llx\n", module_list);
    
    uintptr_t current_entry = module_list;
    int module_count = 0;
    
    printf("[*] Starting module enumeration...\n");
    
    // 只枚举前10个模块
    for (int i = 0; i < 10 && current_entry != 0; i++) {
        printf("[DEBUG] Processing module %d, entry: 0x%llx\n", i, current_entry);
        
        // 验证当前条目地址
        if (current_entry < 0x1000) {
            printf("[DEBUG] Invalid module entry address: 0x%llx, stopping\n", current_entry);
            break;
        }
        
        // 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
        uintptr_t ldr_entry = current_entry - 0x10;
        
        // DllBase 在偏移 0x30
        uintptr_t dll_base = 0;
        if (!read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
            printf("[DEBUG] Failed to read DllBase at entry %d, stopping\n", i);
            break;
        }
        
        // SizeOfImage 在偏移 0x40  
        uintptr_t size_of_image = 0;
        if (!read_virtual_memory(ldr_entry + 0x40, &size_of_image, sizeof(size_of_image))) {
            printf("[DEBUG] Failed to read SizeOfImage at entry %d, stopping\n", i);
            break;
        }
        
        // BaseDllName 在偏移 0x58 (UNICODE_STRING)
        struct {
            USHORT Length;
            USHORT MaximumLength;
            uintptr_t Buffer;
        } unicode_string;
        
        wchar_t module_name[256] = { 0 };
        if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
            if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
                read_virtual_memory(unicode_string.Buffer, module_name, min(unicode_string.Length, sizeof(module_name) - 2));
            }
        }
        
        printf("[+] Module %d:\n", i);
        printf("    Name: %ws\n", module_name);
        printf("    Base: 0x%llx\n", dll_base);
        printf("    Size: 0x%x\n", (unsigned int)size_of_image);
        printf("\n");
        
        // 获取下一个条目
        uintptr_t next_entry = 0;
        if (!read_virtual_memory(current_entry, &next_entry, sizeof(next_entry))) {
            printf("[DEBUG] Failed to read next entry at module %d\n", i);
            break;
        }
        
        printf("[DEBUG] Next entry: 0x%llx\n", next_entry);
        current_entry = next_entry;
        module_count++;
    }
    
    printf("[*] ENUMERATION COMPLETED - Displayed %d modules\n", module_count);
    printf("[*] RETURNING TO MAIN FUNCTION\n");
    fflush(stdout);
}

void eneio_lib::simple_module_test(const char* process_name)
{
	printf("[*] Simple module test for: %s\n", process_name);
	
	// 首先获取进程基址
	uintptr_t base = get_process_base(process_name);
	if (!base) {
		printf("[-] Failed to get process base\n");
		return;
	}
	
	printf("[+] Process base: 0x%llx\n", base);
	
	// 尝试读取PE头
	UINT8 pe_header[64] = { 0 };
	if (read_virtual_memory(base, pe_header, 64)) {
		printf("[+] Successfully read PE header:\n");
		printf("    ");
		for (int i = 0; i < 16; i++) {
			printf("%02X ", pe_header[i]);
		}
		printf("\n");
		
		if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) {
			printf("[+] Valid PE signature found!\n");
		}
	} else {
		printf("[-] Failed to read PE header\n");
	}
}

void eneio_lib::list_running_processes()
{
	printf("[*] Listing running processes...\n");
	
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[-] Failed to get system directory base\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[-] Failed to get initial kprocess\n");
		return;
	}

	printf("\n%-6s %-20s %-16s\n", "PID", "Process Name", "Base Address");
	printf("%-6s %-20s %-16s\n", "------", "--------------------", "----------------");

	// 简化的遍历方法 - 回到原始逻辑但加入重复检测
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	int process_count = 0;
	std::vector<int> seen_pids; // 只检测PID重复
	
	// 读取第一个flink
	read_virtual_memory(flink, &flink, sizeof(PVOID));
	
	do {
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		// 读取进程信息
		char name[16] = { 0 };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));
		
		uintptr_t base_address = 0;
		read_virtual_memory(kprocess + EP_SECTIONBASE, &base_address, sizeof(base_address));
		
		// 验证进程信息并检查重复
		if (process_id > 0 && process_id < 100000 && strlen(name) > 0) {
			bool already_seen = false;
			for (int pid : seen_pids) {
				if (pid == process_id) {
					already_seen = true;
					break;
				}
			}
			
			if (!already_seen) {
				printf("%-6d %-20s 0x%llx\n", process_id, name, base_address);
				seen_pids.push_back(process_id);
				process_count++;
			}
		}
		
		// 获取下一个链表项
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		
		// 安全检查：如果进程数量过多，可能有问题
		if (process_count > 1000) {
			printf("[*] Too many processes, stopping enumeration\n");
			break;
		}
		
	} while (flink != link_start && flink != 0);
	
	printf("\n[+] Total unique processes found: %d\n", process_count);
}

void eneio_lib::search_processes_by_keyword(const char* keyword)
{
	printf("[*] Searching for processes containing keyword: %s\n", keyword);
	
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[-] Failed to get system directory base\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[-] Failed to get initial kprocess\n");
		return;
	}

	const unsigned long limit = 800;
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	int found_count = 0;

	printf("\n%-6s %-30s %-16s\n", "PID", "Process Name", "Base Address");
	printf("%-6s %-30s %-16s\n", "------", "------------------------------", "----------------");

	std::vector<int> seen_pids; // 避免重复
	
	for (int a = 0; a < limit; a++)
	{
		if (a > 0 && flink == link_start) break;
		
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		if (flink == 0) break;
		
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		char name[16] = { 0 };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));
		
		// 检查重复
		bool already_seen = false;
		for (int pid : seen_pids) {
			if (pid == process_id) {
				already_seen = true;
				break;
			}
		}
		if (already_seen) break;
		
		uintptr_t base_address = 0;
		read_virtual_memory(kprocess + EP_SECTIONBASE, &base_address, sizeof(base_address));

		// 检查进程名是否包含关键词（不区分大小写）
		if (process_id > 0 && strlen(name) > 0) {
			seen_pids.push_back(process_id);
			
			char lower_name[16] = { 0 };
			char lower_keyword[256] = { 0 };
			
			// 转换为小写进行比较
			for (int i = 0; i < strlen(name); i++) {
				lower_name[i] = tolower(name[i]);
			}
			for (int i = 0; i < strlen(keyword); i++) {
				lower_keyword[i] = tolower(keyword[i]);
			}
			
			if (strstr(lower_name, lower_keyword) != NULL) {
				printf("%-6d %-30s 0x%llx\n", process_id, name, base_address);
				found_count++;
			}
		}
	}
	
	printf("\n[+] Found %d processes containing '%s'\n", found_count, keyword);
}

uintptr_t eneio_lib::find_module_base(const char* process_name, const wchar_t* module_name)
{
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		return 0;
	}
	
	// PEB + 0x18 = PEB_LDR_DATA
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		return 0;
	}
	
	// PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		return 0;
	}
	
	uintptr_t current_entry = module_list;
	int module_count = 0;
	
	do {
		if (module_count > 100) break;
		
		// 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
		uintptr_t ldr_entry = current_entry - 0x10;
		
		// DllBase 在偏移 0x30
		uintptr_t dll_base = 0;
		if (!read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
			break;
		}
		
		// BaseDllName 在偏移 0x58 (UNICODE_STRING)
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t current_module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, current_module_name, min(unicode_string.Length, sizeof(current_module_name) - 2))) {
					// 比较模块名称（不区分大小写）
					if (wcscmp(current_module_name, module_name) == 0 || 
						_wcsicmp(current_module_name, module_name) == 0) {
						return dll_base;
					}
				}
			}
		}
		
		// 获取下一个条目
		if (!read_virtual_memory(current_entry, &current_entry, sizeof(current_entry))) {
			break;
		}
		
		module_count++;
		
	} while (current_entry != module_list && current_entry != 0);
	
	return 0; // 未找到
}

void eneio_lib::test_simple_module_read(const char* process_name)
{
	printf("[*] Testing simple module read for: %s\n", process_name);
	
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		printf("[-] Failed to get PEB\n");
		return;
	}
	
	printf("[+] PEB: 0x%llx\n", peb);
	
	// 读取 LDR 指针
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		printf("[-] Failed to read LDR pointer\n");
		return;
	}
	
	printf("[+] LDR: 0x%llx\n", ldr_data_ptr);
	
	// 读取模块列表头
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		printf("[-] Failed to read module list\n");
		return;
	}
	
	printf("[+] Module list: 0x%llx\n", module_list);
	
	// 尝试读取第一个模块的基址
	// 需要从链表条目减去0x10偏移得到LDR_DATA_TABLE_ENTRY
	uintptr_t ldr_entry = module_list - 0x10;
	uintptr_t dll_base = 0;
	if (read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
		printf("[+] First module base (corrected): 0x%llx\n", dll_base);
		
		// 尝试读取模块名称
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, module_name, min(unicode_string.Length, sizeof(module_name) - 2))) {
					printf("[+] First module name: %ws\n", module_name);
				}
			}
		}
	} else {
		printf("[-] Failed to read first module base\n");
	}
}



