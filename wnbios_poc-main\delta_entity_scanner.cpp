#define _CRT_SECURE_NO_WARNINGS
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <cmath>
#include <Windows.h>
#include "drv.h"

// 三角洲游戏实体结构定义
struct Vector3 {
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    float distance(const Vector3& other) const {
        float dx = x - other.x;
        float dy = y - other.y;
        float dz = z - other.z;
        return sqrt(dx*dx + dy*dy + dz*dz);
    }
};

struct DeltaEntity {
    uintptr_t entity_ptr;
    Vector3 position;
    Vector3 head_position;
    float health;
    float max_health;
    int team_id;
    int player_state;
    bool is_valid;
    bool is_player;
    std::string name;
    
    DeltaEntity() : entity_ptr(0), health(0), max_health(0), team_id(0), 
                   player_state(0), is_valid(false), is_player(false) {}
};

// 三角洲游戏偏移量（基于技术文档）
namespace DeltaOffsets {
    // 基础偏移
    const uintptr_t ENTITY_LIST = 0x4D28D58;  // 实体列表基址
    const uintptr_t LOCAL_PLAYER = 0x4D28C40; // 本地玩家基址
    const uintptr_t ENTITY_SIZE = 0x8;        // 实体指针大小
    const int MAX_ENTITIES = 128;             // 最大实体数量
    
    // 实体内部偏移
    const uintptr_t POSITION_X = 0x1A0;       // 位置X
    const uintptr_t POSITION_Y = 0x1A4;       // 位置Y  
    const uintptr_t POSITION_Z = 0x1A8;       // 位置Z
    const uintptr_t HEAD_X = 0x1B0;           // 头部位置X
    const uintptr_t HEAD_Y = 0x1B4;           // 头部位置Y
    const uintptr_t HEAD_Z = 0x1B8;           // 头部位置Z
    const uintptr_t HEALTH = 0x140;           // 当前血量
    const uintptr_t MAX_HEALTH = 0x144;       // 最大血量
    const uintptr_t TEAM_ID = 0x3C8;          // 队伍ID
    const uintptr_t PLAYER_STATE = 0x3D8;     // 玩家状态
    const uintptr_t NAME_PTR = 0x450;         // 名称指针
}

class DeltaEntityManager {
private:
    eneio_lib* driver;
    uintptr_t game_base;
    Vector3 local_position;
    
public:
    DeltaEntityManager(eneio_lib* drv) : driver(drv), game_base(0) {
        // 获取游戏进程基址
        game_base = driver->get_process_base("DeltaForce.exe");
        if (!game_base) {
            // 尝试其他可能的进程名
            game_base = driver->get_process_base("df.exe");
        }
        if (!game_base) {
            game_base = driver->get_process_base("delta.exe");
        }
        
        if (game_base) {
            printf("[+] Delta Force base address: 0x%llX\n", game_base);
        } else {
            printf("[-] Delta Force process not found\n");
        }
    }
    
    bool is_valid_entity(uintptr_t entity_ptr) {
        if (!entity_ptr || entity_ptr < 0x10000) return false;
        
        // 检查实体是否有效（读取血量作为验证）
        float health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEALTH);
        return (health > 0 && health <= 1000); // 合理的血量范围
    }
    
    bool is_player_entity(uintptr_t entity_ptr) {
        // 根据PlayerState判断是否为玩家
        int player_state = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::PLAYER_STATE);
        
        // PlayerState值分析（基于技术文档）:
        // 1-50: 真实玩家
        // 51-100: AI/BOT
        // 0或其他: 无效实体
        return (player_state >= 1 && player_state <= 50);
    }
    
    std::string read_entity_name(uintptr_t entity_ptr) {
        uintptr_t name_ptr = driver->read_virtual_memory<uintptr_t>(entity_ptr + DeltaOffsets::NAME_PTR);
        if (!name_ptr) return "Unknown";
        
        char name_buffer[64] = {0};
        if (driver->read_virtual_memory(name_ptr, name_buffer, sizeof(name_buffer) - 1)) {
            return std::string(name_buffer);
        }
        return "Unknown";
    }
    
    DeltaEntity read_entity(uintptr_t entity_ptr) {
        DeltaEntity entity;
        entity.entity_ptr = entity_ptr;
        
        if (!is_valid_entity(entity_ptr)) {
            return entity;
        }
        
        // 读取位置信息
        entity.position.x = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::POSITION_X);
        entity.position.y = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::POSITION_Y);
        entity.position.z = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::POSITION_Z);
        
        // 读取头部位置
        entity.head_position.x = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEAD_X);
        entity.head_position.y = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEAD_Y);
        entity.head_position.z = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEAD_Z);
        
        // 读取血量信息
        entity.health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEALTH);
        entity.max_health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::MAX_HEALTH);
        
        // 读取队伍和状态信息
        entity.team_id = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::TEAM_ID);
        entity.player_state = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::PLAYER_STATE);
        
        // 判断是否为玩家
        entity.is_player = is_player_entity(entity_ptr);
        
        // 读取名称
        entity.name = read_entity_name(entity_ptr);
        
        // 验证数据有效性
        entity.is_valid = (entity.health > 0 && 
                          entity.position.x != 0 && 
                          entity.position.y != 0 && 
                          entity.position.z != 0);
        
        return entity;
    }
    
    std::vector<DeltaEntity> get_all_entities() {
        std::vector<DeltaEntity> entities;
        
        if (!game_base) {
            printf("[-] Game base address not found\n");
            return entities;
        }
        
        // 读取实体列表基址
        uintptr_t entity_list_base = driver->read_virtual_memory<uintptr_t>(game_base + DeltaOffsets::ENTITY_LIST);
        if (!entity_list_base) {
            printf("[-] Entity list base not found\n");
            return entities;
        }
        
        printf("[+] Entity list base: 0x%llX\n", entity_list_base);
        
        // 读取本地玩家位置（用于距离计算）
        uintptr_t local_player_ptr = driver->read_virtual_memory<uintptr_t>(game_base + DeltaOffsets::LOCAL_PLAYER);
        if (local_player_ptr) {
            local_position.x = driver->read_virtual_memory<float>(local_player_ptr + DeltaOffsets::POSITION_X);
            local_position.y = driver->read_virtual_memory<float>(local_player_ptr + DeltaOffsets::POSITION_Y);
            local_position.z = driver->read_virtual_memory<float>(local_player_ptr + DeltaOffsets::POSITION_Z);
        }
        
        // 遍历实体列表
        for (int i = 0; i < DeltaOffsets::MAX_ENTITIES; i++) {
            uintptr_t entity_ptr = driver->read_virtual_memory<uintptr_t>(entity_list_base + i * DeltaOffsets::ENTITY_SIZE);
            
            if (entity_ptr && entity_ptr != local_player_ptr) {
                DeltaEntity entity = read_entity(entity_ptr);
                if (entity.is_valid) {
                    entities.push_back(entity);
                }
            }
        }
        
        return entities;
    }
    
    void print_entity_info(const DeltaEntity& entity) {
        printf("Entity 0x%llX:\n", entity.entity_ptr);
        printf("  Type: %s\n", entity.is_player ? "Player" : "AI/NPC");
        printf("  Name: %s\n", entity.name.c_str());
        printf("  Position: (%.2f, %.2f, %.2f)\n", entity.position.x, entity.position.y, entity.position.z);
        printf("  Head: (%.2f, %.2f, %.2f)\n", entity.head_position.x, entity.head_position.y, entity.head_position.z);
        printf("  Health: %.0f/%.0f\n", entity.health, entity.max_health);
        printf("  Team: %d\n", entity.team_id);
        printf("  PlayerState: %d\n", entity.player_state);
        
        if (local_position.x != 0) {
            float distance = entity.position.distance(local_position);
            printf("  Distance: %.2f\n", distance);
        }
        printf("\n");
    }
};

void print_menu() {
    printf("\n=== Delta Force Entity Scanner ===\n");
    printf("1. Single scan\n");
    printf("2. Continuous monitoring\n");
    printf("3. Filter by distance\n");
    printf("4. Filter by team\n");
    printf("5. Show only players\n");
    printf("6. Show only AI/NPCs\n");
    printf("7. Export to file\n");
    printf("0. Exit\n");
    printf("Choice: ");
}

void continuous_monitoring(DeltaEntityManager& entity_manager, int interval_ms = 1000) {
    printf("[*] Starting continuous monitoring (Press Ctrl+C to stop)...\n");

    int scan_count = 0;
    while (true) {
        system("cls"); // Clear screen on Windows

        printf("=== Scan #%d ===\n", ++scan_count);

        std::vector<DeltaEntity> entities = entity_manager.get_all_entities();

        if (!entities.empty()) {
            int player_count = 0, ai_count = 0;

            for (const auto& entity : entities) {
                if (entity.is_player) player_count++;
                else ai_count++;
            }

            printf("[+] Found %zu entities (Players: %d, AI: %d)\n\n",
                   entities.size(), player_count, ai_count);

            // 显示前10个实体的简要信息
            int display_count = min(10, (int)entities.size());
            for (int i = 0; i < display_count; i++) {
                const auto& entity = entities[i];
                printf("%d. %s [%s] HP:%.0f Team:%d Pos:(%.1f,%.1f,%.1f)\n",
                       i+1, entity.name.c_str(),
                       entity.is_player ? "Player" : "AI",
                       entity.health, entity.team_id,
                       entity.position.x, entity.position.y, entity.position.z);
            }

            if (entities.size() > 10) {
                printf("... and %zu more entities\n", entities.size() - 10);
            }
        } else {
            printf("[-] No entities found\n");
        }

        printf("\nPress Ctrl+C to stop monitoring...\n");
        Sleep(interval_ms);
    }
}

void filter_by_distance(DeltaEntityManager& entity_manager, float max_distance) {
    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();

    printf("[*] Filtering entities within %.2f units...\n", max_distance);

    int count = 0;
    for (const auto& entity : entities) {
        // 需要本地玩家位置来计算距离
        // 这里简化处理，实际应该从entity_manager获取本地位置
        printf("Entity: %s [%s] HP:%.0f\n",
               entity.name.c_str(),
               entity.is_player ? "Player" : "AI",
               entity.health);
        count++;
    }

    printf("[+] Found %d entities within range\n", count);
}

void export_to_file(const std::vector<DeltaEntity>& entities, const std::string& filename) {
    FILE* file = fopen(filename.c_str(), "w");
    if (!file) {
        printf("[-] Failed to create file: %s\n", filename.c_str());
        return;
    }

    fprintf(file, "Delta Force Entity Export\n");
    fprintf(file, "========================\n\n");

    for (const auto& entity : entities) {
        fprintf(file, "Entity: 0x%llX\n", entity.entity_ptr);
        fprintf(file, "Type: %s\n", entity.is_player ? "Player" : "AI/NPC");
        fprintf(file, "Name: %s\n", entity.name.c_str());
        fprintf(file, "Position: (%.2f, %.2f, %.2f)\n",
                entity.position.x, entity.position.y, entity.position.z);
        fprintf(file, "Health: %.0f/%.0f\n", entity.health, entity.max_health);
        fprintf(file, "Team: %d\n", entity.team_id);
        fprintf(file, "PlayerState: %d\n\n", entity.player_state);
    }

    fclose(file);
    printf("[+] Exported %zu entities to %s\n", entities.size(), filename.c_str());
}

int main()
{
    try {
        printf("[*] Initializing Delta Force Entity Scanner...\n");
        eneio_lib driver;

        printf("[+] Driver loaded successfully\n");

        // 创建实体管理器
        DeltaEntityManager entity_manager(&driver);

        int choice;
        while (true) {
            print_menu();
            scanf("%d", &choice);

            switch (choice) {
                case 1: {
                    printf("\n[*] Performing single scan...\n");
                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();

                    if (entities.empty()) {
                        printf("[-] No entities found. Make sure Delta Force is running.\n");
                        break;
                    }

                    printf("[+] Found %zu entities:\n\n", entities.size());

                    int player_count = 0, ai_count = 0;
                    for (const auto& entity : entities) {
                        entity_manager.print_entity_info(entity);
                        if (entity.is_player) player_count++;
                        else ai_count++;
                    }

                    printf("=== Summary ===\n");
                    printf("Total: %zu | Players: %d | AI: %d\n",
                           entities.size(), player_count, ai_count);
                    break;
                }

                case 2: {
                    continuous_monitoring(entity_manager);
                    break;
                }

                case 3: {
                    float distance;
                    printf("Enter maximum distance: ");
                    scanf("%f", &distance);
                    filter_by_distance(entity_manager, distance);
                    break;
                }

                case 4: {
                    int team_id;
                    printf("Enter team ID to filter: ");
                    scanf("%d", &team_id);

                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();
                    printf("[*] Filtering by team %d...\n", team_id);

                    int count = 0;
                    for (const auto& entity : entities) {
                        if (entity.team_id == team_id) {
                            entity_manager.print_entity_info(entity);
                            count++;
                        }
                    }
                    printf("[+] Found %d entities in team %d\n", count, team_id);
                    break;
                }

                case 5: {
                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();
                    printf("[*] Showing only players...\n");

                    int count = 0;
                    for (const auto& entity : entities) {
                        if (entity.is_player) {
                            entity_manager.print_entity_info(entity);
                            count++;
                        }
                    }
                    printf("[+] Found %d players\n", count);
                    break;
                }

                case 6: {
                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();
                    printf("[*] Showing only AI/NPCs...\n");

                    int count = 0;
                    for (const auto& entity : entities) {
                        if (!entity.is_player) {
                            entity_manager.print_entity_info(entity);
                            count++;
                        }
                    }
                    printf("[+] Found %d AI/NPCs\n", count);
                    break;
                }

                case 7: {
                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();
                    if (!entities.empty()) {
                        char filename[256];
                        printf("Enter filename (e.g., entities.txt): ");
                        scanf("%s", filename);
                        export_to_file(entities, filename);
                    } else {
                        printf("[-] No entities to export\n");
                    }
                    break;
                }

                case 0:
                    printf("[+] Exiting...\n");
                    return 0;

                default:
                    printf("[-] Invalid choice\n");
                    break;
            }

            printf("\nPress Enter to continue...");
            getchar(); getchar(); // Clear input buffer
        }

    } catch (const std::exception& e) {
        printf("[-] Error: %s\n", e.what());
        return 1;
    }

    return 0;
}
