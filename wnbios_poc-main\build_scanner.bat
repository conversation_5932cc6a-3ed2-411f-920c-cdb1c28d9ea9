@echo off
echo Building Delta Force Entity Scanner...

REM 检查是否有Visual Studio环境
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo Visual Studio compiler not found. Please run this from Visual Studio Developer Command Prompt.
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "build" mkdir build

REM 编译简单版本
echo Compiling simple scanner...
cl /EHsc /std:c++17 /I. delta_entity_scanner.cpp drv.cpp /Fe:build\delta_simple_scanner.exe /link kernel32.lib user32.lib

REM 编译高级版本
echo Compiling advanced scanner...
cl /EHsc /std:c++17 /I. delta_advanced_scanner.cpp drv.cpp /Fe:build\delta_advanced_scanner.exe /link kernel32.lib user32.lib

if %errorlevel% equ 0 (
    echo.
    echo Build successful!
    echo Output files:
    echo   - build\delta_simple_scanner.exe
    echo   - build\delta_advanced_scanner.exe
    echo.
    echo Usage:
    echo   1. Run as Administrator
    echo   2. Start Delta Force game
    echo   3. Run the scanner executable
) else (
    echo.
    echo Build failed! Check the error messages above.
)

pause
