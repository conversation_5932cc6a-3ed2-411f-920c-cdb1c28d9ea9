#define _CRT_SECURE_NO_WARNINGS
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <cmath>
#include <algorithm>
#include <fstream>
#include <ctime>
#include <Windows.h>
#include "drv.h"
#include "delta_scanner.h"

// 实现Vector3距离计算
float Vector3::distance(const Vector3& other) const {
    float dx = x - other.x;
    float dy = y - other.y;
    float dz = z - other.z;
    return sqrt(dx*dx + dy*dy + dz*dz);
}

// 实现DeltaEntity构造函数
DeltaEntity::DeltaEntity() : entity_ptr(0), health(0), max_health(0), armor(0),
                            team_id(0), player_state(0), weapon_id(0), 
                            is_valid(false), is_player(false), is_alive(false), is_visible(false) {}

// 三角洲游戏偏移量定义（基于技术文档v2.0）
namespace DeltaOffsets {
    // 基础偏移量
    const uintptr_t ENTITY_LIST = 0x4D28D58;    // 实体列表基址
    const uintptr_t LOCAL_PLAYER = 0x4D28C40;   // 本地玩家基址
    const uintptr_t GAME_STATE = 0x4D28E00;     // 游戏状态基址
    const uintptr_t VIEW_MATRIX = 0x4D29000;    // 视图矩阵基址
    const uintptr_t ENTITY_SIZE = 0x8;          // 实体指针大小
    const int MAX_ENTITIES = 128;               // 最大实体数量
    
    // 实体偏移量
    const uintptr_t POSITION_X = 0x1A0;         // 位置X
    const uintptr_t POSITION_Y = 0x1A4;         // 位置Y  
    const uintptr_t POSITION_Z = 0x1A8;         // 位置Z
    const uintptr_t HEAD_X = 0x1B0;             // 头部位置X
    const uintptr_t HEAD_Y = 0x1B4;             // 头部位置Y
    const uintptr_t HEAD_Z = 0x1B8;             // 头部位置Z
    const uintptr_t VELOCITY_X = 0x1C0;         // 速度X
    const uintptr_t VELOCITY_Y = 0x1C4;         // 速度Y
    const uintptr_t VELOCITY_Z = 0x1C8;         // 速度Z
    const uintptr_t HEALTH = 0x140;             // 当前血量
    const uintptr_t MAX_HEALTH = 0x144;         // 最大血量
    const uintptr_t ARMOR = 0x148;              // 护甲值
    const uintptr_t TEAM_ID = 0x3C8;            // 队伍ID
    const uintptr_t PLAYER_STATE = 0x3D8;       // 玩家状态
    const uintptr_t WEAPON_ID = 0x400;          // 武器ID
    const uintptr_t NAME_PTR = 0x450;           // 名称指针
    const uintptr_t IS_VISIBLE = 0x500;         // 可见性标志
}

// 实现DeltaEntityManager
DeltaEntityManager::DeltaEntityManager(eneio_lib* drv) : driver(drv), game_base(0) {
    // 尝试多个可能的进程名
    std::vector<std::string> process_names = {
        "DeltaForce.exe", "df.exe", "delta.exe", "Delta.exe", "deltaforce.exe"
    };
    
    for (const auto& name : process_names) {
        game_base = driver->get_process_base(name.c_str());
        if (game_base) {
            printf("[+] Found Delta Force process: %s (Base: 0x%llX)\n", name.c_str(), game_base);
            break;
        }
    }
    
    if (!game_base) {
        printf("[-] Delta Force process not found. Tried processes:\n");
        for (const auto& name : process_names) {
            printf("    - %s\n", name.c_str());
        }
    }
}

bool DeltaEntityManager::is_valid_entity(uintptr_t entity_ptr) {
    if (!entity_ptr || entity_ptr < 0x10000) return false;
    
    // 检查实体是否有效
    float health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEALTH);
    float max_health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::MAX_HEALTH);
    
    return (health >= 0 && health <= 1000 && max_health > 0 && max_health <= 1000);
}

bool DeltaEntityManager::is_player_entity(uintptr_t entity_ptr) {
    int player_state = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::PLAYER_STATE);
    
    // PlayerState值分析（基于技术文档）:
    // 1-50: 真实玩家
    // 51-100: AI/BOT  
    // 0或其他: 无效实体
    return (player_state >= 1 && player_state <= 50);
}

std::string DeltaEntityManager::read_entity_name(uintptr_t entity_ptr) {
    uintptr_t name_ptr = driver->read_virtual_memory<uintptr_t>(entity_ptr + DeltaOffsets::NAME_PTR);
    if (!name_ptr) return "Unknown";
    
    char name_buffer[64] = {0};
    if (driver->read_virtual_memory(name_ptr, name_buffer, sizeof(name_buffer) - 1)) {
        return std::string(name_buffer);
    }
    return "Unknown";
}

void DeltaEntityManager::update_game_state() {
    if (!game_base) return;
    
    // 读取本地玩家信息
    uintptr_t local_player_ptr = driver->read_virtual_memory<uintptr_t>(game_base + DeltaOffsets::LOCAL_PLAYER);
    if (local_player_ptr) {
        game_state.local_position.x = driver->read_virtual_memory<float>(local_player_ptr + DeltaOffsets::POSITION_X);
        game_state.local_position.y = driver->read_virtual_memory<float>(local_player_ptr + DeltaOffsets::POSITION_Y);
        game_state.local_position.z = driver->read_virtual_memory<float>(local_player_ptr + DeltaOffsets::POSITION_Z);
        game_state.local_team = driver->read_virtual_memory<int>(local_player_ptr + DeltaOffsets::TEAM_ID);
        game_state.in_game = !game_state.local_position.is_zero();
    }
    
    // 读取游戏状态信息
    uintptr_t game_state_ptr = driver->read_virtual_memory<uintptr_t>(game_base + DeltaOffsets::GAME_STATE);
    if (game_state_ptr) {
        game_state.round_time = driver->read_virtual_memory<int>(game_state_ptr + 0x10);
        game_state.score_team1 = driver->read_virtual_memory<int>(game_state_ptr + 0x20);
        game_state.score_team2 = driver->read_virtual_memory<int>(game_state_ptr + 0x24);
    }
}

DeltaEntity DeltaEntityManager::read_entity(uintptr_t entity_ptr) {
    DeltaEntity entity;
    entity.entity_ptr = entity_ptr;
    
    if (!is_valid_entity(entity_ptr)) {
        return entity;
    }
    
    // 读取位置信息
    entity.position.x = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::POSITION_X);
    entity.position.y = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::POSITION_Y);
    entity.position.z = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::POSITION_Z);
    
    // 读取头部位置
    entity.head_position.x = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEAD_X);
    entity.head_position.y = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEAD_Y);
    entity.head_position.z = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEAD_Z);
    
    // 读取速度信息
    entity.velocity.x = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::VELOCITY_X);
    entity.velocity.y = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::VELOCITY_Y);
    entity.velocity.z = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::VELOCITY_Z);
    
    // 读取血量和护甲
    entity.health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::HEALTH);
    entity.max_health = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::MAX_HEALTH);
    entity.armor = driver->read_virtual_memory<float>(entity_ptr + DeltaOffsets::ARMOR);
    
    // 读取队伍和状态信息
    entity.team_id = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::TEAM_ID);
    entity.player_state = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::PLAYER_STATE);
    entity.weapon_id = driver->read_virtual_memory<int>(entity_ptr + DeltaOffsets::WEAPON_ID);
    
    // 读取可见性
    entity.is_visible = driver->read_virtual_memory<bool>(entity_ptr + DeltaOffsets::IS_VISIBLE);
    
    // 判断类型和状态
    entity.is_player = is_player_entity(entity_ptr);
    entity.is_alive = (entity.health > 0);
    entity.name = read_entity_name(entity_ptr);
    
    // 验证数据有效性
    entity.is_valid = (entity.health >= 0 && 
                      !entity.position.is_zero() &&
                      entity.max_health > 0);
    
    return entity;
}

std::vector<DeltaEntity> DeltaEntityManager::get_all_entities() {
    std::vector<DeltaEntity> entities;
    
    if (!game_base) {
        printf("[-] Game base address not found\n");
        return entities;
    }
    
    // 更新游戏状态
    update_game_state();
    
    // 读取实体列表基址
    uintptr_t entity_list_base = driver->read_virtual_memory<uintptr_t>(game_base + DeltaOffsets::ENTITY_LIST);
    if (!entity_list_base) {
        printf("[-] Entity list base not found\n");
        return entities;
    }
    
    // 读取本地玩家指针用于排除
    uintptr_t local_player_ptr = driver->read_virtual_memory<uintptr_t>(game_base + DeltaOffsets::LOCAL_PLAYER);
    
    // 遍历实体列表
    for (int i = 0; i < DeltaOffsets::MAX_ENTITIES; i++) {
        uintptr_t entity_ptr = driver->read_virtual_memory<uintptr_t>(entity_list_base + i * DeltaOffsets::ENTITY_SIZE);
        
        if (entity_ptr && entity_ptr != local_player_ptr) {
            DeltaEntity entity = read_entity(entity_ptr);
            if (entity.is_valid) {
                entities.push_back(entity);
            }
        }
    }
    
    return entities;
}

// 过滤功能实现
std::vector<DeltaEntity> DeltaEntityManager::filter_by_distance(const std::vector<DeltaEntity>& entities, float max_distance) {
    std::vector<DeltaEntity> filtered;
    
    for (const auto& entity : entities) {
        float distance = entity.position.distance(game_state.local_position);
        if (distance <= max_distance) {
            filtered.push_back(entity);
        }
    }
    
    return filtered;
}

std::vector<DeltaEntity> DeltaEntityManager::filter_by_team(const std::vector<DeltaEntity>& entities, int team_id) {
    std::vector<DeltaEntity> filtered;
    
    for (const auto& entity : entities) {
        if (entity.team_id == team_id) {
            filtered.push_back(entity);
        }
    }
    
    return filtered;
}

std::vector<DeltaEntity> DeltaEntityManager::filter_players_only(const std::vector<DeltaEntity>& entities) {
    std::vector<DeltaEntity> filtered;
    
    for (const auto& entity : entities) {
        if (entity.is_player) {
            filtered.push_back(entity);
        }
    }
    
    return filtered;
}

std::vector<DeltaEntity> DeltaEntityManager::filter_enemies_only(const std::vector<DeltaEntity>& entities) {
    std::vector<DeltaEntity> filtered;
    
    for (const auto& entity : entities) {
        if (entity.is_enemy(game_state.local_team)) {
            filtered.push_back(entity);
        }
    }
    
    return filtered;
}

std::vector<DeltaEntity> DeltaEntityManager::filter_alive_only(const std::vector<DeltaEntity>& entities) {
    std::vector<DeltaEntity> filtered;
    
    for (const auto& entity : entities) {
        if (entity.is_alive) {
            filtered.push_back(entity);
        }
    }
    
    return filtered;
}

void DeltaEntityManager::print_entity_info(const DeltaEntity& entity) {
    printf("┌─ Entity 0x%llX ─────────────────────────────────────────┐\n", entity.entity_ptr);
    printf("│ Type: %-10s │ Name: %-20s │ Team: %d    │\n", 
           entity.is_player ? "Player" : "AI/NPC", entity.name.c_str(), entity.team_id);
    printf("│ Position: (%7.1f, %7.1f, %7.1f)                    │\n", 
           entity.position.x, entity.position.y, entity.position.z);
    printf("│ Head Pos: (%7.1f, %7.1f, %7.1f)                    │\n", 
           entity.head_position.x, entity.head_position.y, entity.head_position.z);
    printf("│ Health: %3.0f/%3.0f (%.1f%%) │ Armor: %3.0f │ Weapon: %d     │\n", 
           entity.health, entity.max_health, entity.health_percentage(), entity.armor, entity.weapon_id);
    printf("│ Status: %s │ Visible: %s │ PlayerState: %d │\n",
           entity.is_alive ? "Alive" : "Dead ", entity.is_visible ? "Yes" : "No ", entity.player_state);
    
    if (!game_state.local_position.is_zero()) {
        float distance = entity.position.distance(game_state.local_position);
        printf("│ Distance: %7.1f units                                   │\n", distance);
    }
    
    printf("└─────────────────────────────────────────────────────────┘\n\n");
}

void DeltaEntityManager::print_entity_summary(const DeltaEntity& entity) {
    float distance = game_state.local_position.is_zero() ? 0 : 
                    entity.position.distance(game_state.local_position);
    
    printf("%-15s [%s] HP:%3.0f/%3.0f T:%d D:%6.1f %s\n",
           entity.name.c_str(),
           entity.is_player ? "P" : "A",
           entity.health, entity.max_health,
           entity.team_id, distance,
           entity.is_alive ? "✓" : "✗");
}

void DeltaEntityManager::print_game_state() {
    printf("=== Game State ===\n");
    printf("In Game: %s\n", game_state.in_game ? "Yes" : "No");
    printf("Local Team: %d\n", game_state.local_team);
    printf("Local Position: (%.1f, %.1f, %.1f)\n",
           game_state.local_position.x, game_state.local_position.y, game_state.local_position.z);
    printf("Round Time: %d seconds\n", game_state.round_time);
    printf("Score: Team1=%d, Team2=%d\n", game_state.score_team1, game_state.score_team2);
    printf("==================\n\n");
}

// 实用函数实现
void clear_screen() {
    system("cls");
}

std::string get_timestamp() {
    time_t rawtime;
    struct tm* timeinfo;
    char buffer[80];

    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);

    return std::string(buffer);
}

std::vector<DeltaEntity> apply_filters(const std::vector<DeltaEntity>& entities,
                                      const ScannerConfig& config,
                                      const GameState& game_state) {
    std::vector<DeltaEntity> filtered = entities;

    // 过滤存活状态
    if (config.show_alive_only) {
        std::vector<DeltaEntity> alive_entities;
        for (const auto& entity : filtered) {
            if (entity.is_alive) {
                alive_entities.push_back(entity);
            }
        }
        filtered = alive_entities;
    }

    // 过滤玩家类型
    if (!config.show_players || !config.show_ai) {
        std::vector<DeltaEntity> type_filtered;
        for (const auto& entity : filtered) {
            if ((config.show_players && entity.is_player) ||
                (config.show_ai && !entity.is_player)) {
                type_filtered.push_back(entity);
            }
        }
        filtered = type_filtered;
    }

    // 过滤敌人
    if (config.show_enemies_only) {
        std::vector<DeltaEntity> enemy_filtered;
        for (const auto& entity : filtered) {
            if (entity.is_enemy(game_state.local_team)) {
                enemy_filtered.push_back(entity);
            }
        }
        filtered = enemy_filtered;
    }

    // 过滤距离
    if (config.max_distance > 0 && !game_state.local_position.is_zero()) {
        std::vector<DeltaEntity> distance_filtered;
        for (const auto& entity : filtered) {
            float distance = entity.position.distance(game_state.local_position);
            if (distance <= config.max_distance) {
                distance_filtered.push_back(entity);
            }
        }
        filtered = distance_filtered;
    }

    // 过滤特定队伍
    if (config.target_team >= 0) {
        std::vector<DeltaEntity> team_filtered;
        for (const auto& entity : filtered) {
            if (entity.team_id == config.target_team) {
                team_filtered.push_back(entity);
            }
        }
        filtered = team_filtered;
    }

    return filtered;
}

void print_statistics(const std::vector<DeltaEntity>& entities) {
    int total = entities.size();
    int players = 0, ai = 0, alive = 0, dead = 0;
    int team_counts[10] = {0}; // 支持最多10个队伍
    float total_health = 0, avg_health = 0;

    for (const auto& entity : entities) {
        if (entity.is_player) players++;
        else ai++;

        if (entity.is_alive) alive++;
        else dead++;

        if (entity.team_id >= 0 && entity.team_id < 10) {
            team_counts[entity.team_id]++;
        }

        total_health += entity.health;
    }

    if (total > 0) {
        avg_health = total_health / total;
    }

    printf("\n┌─ Statistics ─────────────────────────────────────────┐\n");
    printf("│ Total Entities: %-3d │ Players: %-3d │ AI: %-3d      │\n", total, players, ai);
    printf("│ Alive: %-3d │ Dead: %-3d │ Avg Health: %-6.1f    │\n", alive, dead, avg_health);
    printf("│ Team Distribution:                                   │\n");

    for (int i = 0; i < 10; i++) {
        if (team_counts[i] > 0) {
            printf("│   Team %d: %-3d entities                             │\n", i, team_counts[i]);
        }
    }

    printf("└──────────────────────────────────────────────────────┘\n\n");
}

void export_entities(const std::vector<DeltaEntity>& entities, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        printf("[-] Failed to create file: %s\n", filename.c_str());
        return;
    }

    file << "Delta Force Entity Export\n";
    file << "Generated: " << get_timestamp() << "\n";
    file << "Total Entities: " << entities.size() << "\n";
    file << "========================\n\n";

    for (const auto& entity : entities) {
        file << "Entity: 0x" << std::hex << entity.entity_ptr << std::dec << "\n";
        file << "Type: " << (entity.is_player ? "Player" : "AI/NPC") << "\n";
        file << "Name: " << entity.name << "\n";
        file << "Position: (" << entity.position.x << ", " << entity.position.y << ", " << entity.position.z << ")\n";
        file << "Head Position: (" << entity.head_position.x << ", " << entity.head_position.y << ", " << entity.head_position.z << ")\n";
        file << "Velocity: (" << entity.velocity.x << ", " << entity.velocity.y << ", " << entity.velocity.z << ")\n";
        file << "Health: " << entity.health << "/" << entity.max_health << " (" << entity.health_percentage() << "%)\n";
        file << "Armor: " << entity.armor << "\n";
        file << "Team: " << entity.team_id << "\n";
        file << "PlayerState: " << entity.player_state << "\n";
        file << "Weapon ID: " << entity.weapon_id << "\n";
        file << "Status: " << (entity.is_alive ? "Alive" : "Dead") << "\n";
        file << "Visible: " << (entity.is_visible ? "Yes" : "No") << "\n";
        file << "\n";
    }

    file.close();
    printf("[+] Exported %zu entities to %s\n", entities.size(), filename.c_str());
}

void print_menu() {
    printf("\n╔═══════════════════════════════════════════════════════╗\n");
    printf("║              Delta Force Entity Scanner              ║\n");
    printf("╠═══════════════════════════════════════════════════════╣\n");
    printf("║ 1. Single scan                                       ║\n");
    printf("║ 2. Continuous monitoring                              ║\n");
    printf("║ 3. Configure filters                                 ║\n");
    printf("║ 4. Show game state                                   ║\n");
    printf("║ 5. Export entities to file                           ║\n");
    printf("║ 6. Show statistics                                   ║\n");
    printf("║ 7. Advanced search                                   ║\n");
    printf("║ 0. Exit                                              ║\n");
    printf("╚═══════════════════════════════════════════════════════╝\n");
    printf("Choice: ");
}

void single_scan(DeltaEntityManager& manager, const ScannerConfig& config) {
    printf("\n[*] Performing single scan...\n");

    std::vector<DeltaEntity> entities = manager.get_all_entities();
    if (entities.empty()) {
        printf("[-] No entities found. Make sure Delta Force is running.\n");
        return;
    }

    // 应用过滤器
    std::vector<DeltaEntity> filtered = apply_filters(entities, config, manager.get_game_state());

    printf("[+] Found %zu entities (filtered: %zu):\n\n", entities.size(), filtered.size());

    // 显示实体信息
    for (const auto& entity : filtered) {
        manager.print_entity_summary(entity);
    }

    print_statistics(filtered);
}

void continuous_monitoring(DeltaEntityManager& manager, const ScannerConfig& config) {
    printf("[*] Starting continuous monitoring (Press Ctrl+C to stop)...\n");
    printf("Update interval: %d ms\n", config.update_interval_ms);

    int scan_count = 0;
    while (true) {
        clear_screen();

        printf("╔═══════════════════════════════════════════════════════╗\n");
        printf("║           Continuous Monitoring - Scan #%-4d         ║\n", ++scan_count);
        printf("║                  %s                 ║\n", get_timestamp().c_str());
        printf("╚═══════════════════════════════════════════════════════╝\n\n");

        std::vector<DeltaEntity> entities = manager.get_all_entities();

        if (!entities.empty()) {
            std::vector<DeltaEntity> filtered = apply_filters(entities, config, manager.get_game_state());

            printf("Entities: %zu total, %zu filtered\n\n", entities.size(), filtered.size());

            // 显示前15个实体的简要信息
            int display_count = std::min(15, (int)filtered.size());
            for (int i = 0; i < display_count; i++) {
                printf("%2d. ", i+1);
                manager.print_entity_summary(filtered[i]);
            }

            if (filtered.size() > 15) {
                printf("... and %zu more entities\n", filtered.size() - 15);
            }

            // 显示游戏状态
            manager.print_game_state();
        } else {
            printf("[-] No entities found\n");
        }

        printf("Press Ctrl+C to stop monitoring...\n");
        Sleep(config.update_interval_ms);
    }
}

void configure_filters(ScannerConfig& config) {
    int choice;

    while (true) {
        printf("\n╔═══════════════════════════════════════════════════════╗\n");
        printf("║                  Filter Configuration                ║\n");
        printf("╠═══════════════════════════════════════════════════════╣\n");
        printf("║ Current Settings:                                     ║\n");
        printf("║   Show Players: %-3s │ Show AI: %-3s                ║\n",
               config.show_players ? "Yes" : "No", config.show_ai ? "Yes" : "No");
        printf("║   Enemies Only: %-3s │ Alive Only: %-3s             ║\n",
               config.show_enemies_only ? "Yes" : "No", config.show_alive_only ? "Yes" : "No");
        printf("║   Max Distance: %-6.1f │ Target Team: %-2d           ║\n",
               config.max_distance, config.target_team);
        printf("║   Update Interval: %-4d ms                           ║\n", config.update_interval_ms);
        printf("╠═══════════════════════════════════════════════════════╣\n");
        printf("║ 1. Toggle show players                               ║\n");
        printf("║ 2. Toggle show AI                                    ║\n");
        printf("║ 3. Toggle enemies only                               ║\n");
        printf("║ 4. Toggle alive only                                 ║\n");
        printf("║ 5. Set max distance                                  ║\n");
        printf("║ 6. Set target team                                   ║\n");
        printf("║ 7. Set update interval                               ║\n");
        printf("║ 8. Reset to defaults                                 ║\n");
        printf("║ 0. Back to main menu                                 ║\n");
        printf("╚═══════════════════════════════════════════════════════╝\n");
        printf("Choice: ");

        scanf("%d", &choice);

        switch (choice) {
            case 1:
                config.show_players = !config.show_players;
                printf("[+] Show players: %s\n", config.show_players ? "Enabled" : "Disabled");
                break;

            case 2:
                config.show_ai = !config.show_ai;
                printf("[+] Show AI: %s\n", config.show_ai ? "Enabled" : "Disabled");
                break;

            case 3:
                config.show_enemies_only = !config.show_enemies_only;
                printf("[+] Enemies only: %s\n", config.show_enemies_only ? "Enabled" : "Disabled");
                break;

            case 4:
                config.show_alive_only = !config.show_alive_only;
                printf("[+] Alive only: %s\n", config.show_alive_only ? "Enabled" : "Disabled");
                break;

            case 5:
                printf("Enter max distance (0 = no limit): ");
                scanf("%f", &config.max_distance);
                printf("[+] Max distance set to: %.1f\n", config.max_distance);
                break;

            case 6:
                printf("Enter target team ID (-1 = all teams): ");
                scanf("%d", &config.target_team);
                printf("[+] Target team set to: %d\n", config.target_team);
                break;

            case 7:
                printf("Enter update interval (ms): ");
                scanf("%d", &config.update_interval_ms);
                printf("[+] Update interval set to: %d ms\n", config.update_interval_ms);
                break;

            case 8:
                config = ScannerConfig(); // Reset to defaults
                printf("[+] Filters reset to defaults\n");
                break;

            case 0:
                return;

            default:
                printf("[-] Invalid choice\n");
                break;
        }

        printf("\nPress Enter to continue...");
        getchar(); getchar();
    }
}

void advanced_search(DeltaEntityManager& manager) {
    printf("\n╔═══════════════════════════════════════════════════════╗\n");
    printf("║                   Advanced Search                    ║\n");
    printf("╚═══════════════════════════════════════════════════════╝\n");

    std::vector<DeltaEntity> entities = manager.get_all_entities();
    if (entities.empty()) {
        printf("[-] No entities found\n");
        return;
    }

    int choice;
    printf("1. Search by name\n");
    printf("2. Find closest entity\n");
    printf("3. Find entities by health range\n");
    printf("4. Find entities with specific weapon\n");
    printf("5. Find moving entities\n");
    printf("Choice: ");
    scanf("%d", &choice);

    switch (choice) {
        case 1: {
            char search_name[64];
            printf("Enter name to search: ");
            scanf("%s", search_name);

            printf("\nSearch results for '%s':\n", search_name);
            int found = 0;
            for (const auto& entity : entities) {
                if (entity.name.find(search_name) != std::string::npos) {
                    manager.print_entity_info(entity);
                    found++;
                }
            }
            printf("Found %d matching entities\n", found);
            break;
        }

        case 2: {
            if (manager.get_game_state().local_position.is_zero()) {
                printf("[-] Local position not available\n");
                break;
            }

            float min_distance = FLT_MAX;
            DeltaEntity closest;

            for (const auto& entity : entities) {
                float distance = entity.position.distance(manager.get_game_state().local_position);
                if (distance < min_distance) {
                    min_distance = distance;
                    closest = entity;
                }
            }

            if (closest.is_valid) {
                printf("\nClosest entity (%.1f units):\n", min_distance);
                manager.print_entity_info(closest);
            }
            break;
        }

        case 3: {
            float min_health, max_health;
            printf("Enter health range (min max): ");
            scanf("%f %f", &min_health, &max_health);

            printf("\nEntities with health between %.0f and %.0f:\n", min_health, max_health);
            int found = 0;
            for (const auto& entity : entities) {
                if (entity.health >= min_health && entity.health <= max_health) {
                    manager.print_entity_summary(entity);
                    found++;
                }
            }
            printf("Found %d matching entities\n", found);
            break;
        }

        case 4: {
            int weapon_id;
            printf("Enter weapon ID: ");
            scanf("%d", &weapon_id);

            printf("\nEntities with weapon ID %d:\n", weapon_id);
            int found = 0;
            for (const auto& entity : entities) {
                if (entity.weapon_id == weapon_id) {
                    manager.print_entity_summary(entity);
                    found++;
                }
            }
            printf("Found %d matching entities\n", found);
            break;
        }

        case 5: {
            float min_speed;
            printf("Enter minimum speed: ");
            scanf("%f", &min_speed);

            printf("\nMoving entities (speed > %.1f):\n", min_speed);
            int found = 0;
            for (const auto& entity : entities) {
                float speed = sqrt(entity.velocity.x * entity.velocity.x +
                                 entity.velocity.y * entity.velocity.y +
                                 entity.velocity.z * entity.velocity.z);
                if (speed > min_speed) {
                    printf("Speed: %.1f - ", speed);
                    manager.print_entity_summary(entity);
                    found++;
                }
            }
            printf("Found %d moving entities\n", found);
            break;
        }

        default:
            printf("[-] Invalid choice\n");
            break;
    }
}

int main()
{
    try {
        printf("╔═══════════════════════════════════════════════════════╗\n");
        printf("║        Delta Force Advanced Entity Scanner           ║\n");
        printf("║              Based on wnBios64 Driver                ║\n");
        printf("╚═══════════════════════════════════════════════════════╝\n\n");

        printf("[*] Initializing driver...\n");
        eneio_lib driver;

        printf("[+] Driver loaded successfully\n");

        // 创建实体管理器
        DeltaEntityManager entity_manager(&driver);

        if (!entity_manager.is_game_running()) {
            printf("[-] Delta Force not detected. Please start the game first.\n");
            printf("Press Enter to exit...");
            getchar();
            return 1;
        }

        // 默认配置
        ScannerConfig config;

        int choice;
        while (true) {
            print_menu();
            scanf("%d", &choice);

            switch (choice) {
                case 1:
                    single_scan(entity_manager, config);
                    break;

                case 2:
                    continuous_monitoring(entity_manager, config);
                    break;

                case 3:
                    configure_filters(config);
                    break;

                case 4:
                    entity_manager.print_game_state();
                    break;

                case 5: {
                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();
                    if (!entities.empty()) {
                        std::vector<DeltaEntity> filtered = apply_filters(entities, config, entity_manager.get_game_state());

                        char filename[256];
                        printf("Enter filename (e.g., entities.txt): ");
                        scanf("%s", filename);
                        export_entities(filtered, filename);
                    } else {
                        printf("[-] No entities to export\n");
                    }
                    break;
                }

                case 6: {
                    std::vector<DeltaEntity> entities = entity_manager.get_all_entities();
                    if (!entities.empty()) {
                        std::vector<DeltaEntity> filtered = apply_filters(entities, config, entity_manager.get_game_state());
                        print_statistics(filtered);
                    } else {
                        printf("[-] No entities found\n");
                    }
                    break;
                }

                case 7:
                    advanced_search(entity_manager);
                    break;

                case 0:
                    printf("[+] Exiting...\n");
                    return 0;

                default:
                    printf("[-] Invalid choice\n");
                    break;
            }

            printf("\nPress Enter to continue...");
            getchar(); getchar(); // Clear input buffer
        }

    } catch (const std::exception& e) {
        printf("[-] Error: %s\n", e.what());
        printf("Press Enter to exit...");
        getchar();
        return 1;
    }

    return 0;
}
